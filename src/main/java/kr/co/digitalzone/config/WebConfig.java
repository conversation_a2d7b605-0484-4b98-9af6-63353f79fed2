package kr.co.digitalzone.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.AsyncSupportConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.concurrent.Executors;

@Configuration
@Slf4j
public class WebConfig implements WebMvcConfigurer {

    @Override
    public void configureAsyncSupport(AsyncSupportConfigurer configurer) {
        // 비동기 요청 타임아웃 설정 (30초)
        configurer.setDefaultTimeout(30000);
        
        // 비동기 처리용 스레드 풀 설정
        configurer.setTaskExecutor(Executors.newFixedThreadPool(10));
        
        log.info("비동기 지원 설정 완료 - 타임아웃: 30초, 스레드 풀: 10개");
    }
}
