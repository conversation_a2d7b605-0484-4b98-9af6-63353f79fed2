package kr.co.digitalzone.repository;

import kr.co.digitalzone.dto.KeywordCountDto;
import kr.co.digitalzone.entity.ImageMetadata;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ImageMetadataRepository extends JpaRepository<ImageMetadata, String> {
    
    /**
     * 모든 키워드별 이미지 개수 조회
     * @return 키워드별 이미지 개수 리스트 (개수 내림차순 정렬)
     */
    @Query("SELECT new kr.co.digitalzone.dto.KeywordCountDto(im.keyword, COUNT(im.id)) " +
           "FROM ImageMetadata im " +
           "GROUP BY im.keyword ")
    List<KeywordCountDto> findKeywordCounts();

//    /**
//     * 특정 키워드와 소스의 이미지 개수 조회
//     */
//    @Query("SELECT COUNT(im.id) FROM ImageMetadata im WHERE im.keyword = :keyword AND im.source = :source")
//    Long countByKeywordAndSource(@Param("keyword") String keyword, @Param("source") String source);

    @Query("SELECT COUNT(im.id) FROM ImageMetadata im WHERE im.jobId = :jobId")
    Long countByJobId(@Param("jobId") String jobId);

    /**
     * 특정 job_id의 이미지 메타데이터 삭제
     */
    @Modifying
    @Query("DELETE FROM ImageMetadata im WHERE im.jobId = :jobId")
    void deleteByJobId(@Param("jobId") String jobId);

}


