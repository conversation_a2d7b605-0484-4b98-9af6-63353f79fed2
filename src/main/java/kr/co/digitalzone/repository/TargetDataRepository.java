package kr.co.digitalzone.repository;

import kr.co.digitalzone.dto.CrawlingCountDto;
import kr.co.digitalzone.entity.TargetData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TargetDataRepository extends JpaRepository<TargetData, Long> {

    /**
     * 모든 타겟 데이터 조회 (최신순)
     */
    @Query("SELECT new kr.co.digitalzone.dto.CrawlingCountDto(td.source, td.keyword, td.targetCount) " +
            "FROM TargetData td ")
    List<CrawlingCountDto> findAllTargetData();

    /**
     * 특정 job_id로 타겟 데이터 조회
     * Spring Data JPA 메서드 네이밍 규칙에 의해 자동으로 구현됨
     */
    List<TargetData> findByJobId(String jobId);

    /**
     * 여러 job_id들로 타겟 데이터 조회
     * @param jobIds 조회할 job_id 목록
     * @return 해당 job_id들의 타겟 데이터 목록
     */
    List<TargetData> findByJobIdIn(List<String> jobIds);

    /**
     * 특정 job_id의 타겟 데이터 삭제
     */
    @Modifying
    @Query("DELETE FROM TargetData td WHERE td.jobId = :jobId")
    void deleteByJobId(@Param("jobId") String jobId);

    /**
     * 모든 타겟 데이터 조회 (최신순) - 실시간 모니터링용
     */
    @Query("SELECT td FROM TargetData td ORDER BY td.startDate DESC")
    List<TargetData> findAllOrderByStartDateDesc();

    /**
     * 최신 job_id들을 조회 (중복 제거, 최신순)
     * 최근에 생성된 job_id들을 자동으로 조회하기 위해 사용
     */
    @Query(value = "SELECT DISTINCT job_id FROM target_data GROUP BY job_id ORDER BY MAX(start_date) DESC", nativeQuery = true)
    List<String> findLatestJobIds();

    /**
     * 최신 N개의 job_id들을 조회 (중복 제거, 최신순)
     * @param limit 조회할 job_id 개수
     */
    @Query(value = "SELECT DISTINCT job_id FROM target_data GROUP BY job_id ORDER BY MAX(start_date) DESC LIMIT :limit", nativeQuery = true)
    List<String> findLatestJobIds(@Param("limit") int limit);

//    /**
//     * 진행 중인 작업만 조회 (실시간 모니터링용)
//     */
//    @Query("SELECT td FROM TargetData td WHERE td.stats IN ('수집 중', '대기 중') ORDER BY td.startDate DESC")
//    List<TargetData> findActiveJobs();
//
//    /**
//     * 특정 상태의 작업 조회
//     */
//    @Query("SELECT td FROM TargetData td WHERE td.stats = :status ORDER BY td.startDate DESC")
//    List<TargetData> findByStatus(@Param("status") String status);

}